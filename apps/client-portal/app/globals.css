@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --sidebar-width: 280px;
  --navbar-height: 50px;
  --footer-height: 112px;
  --content: calc(100vh - var(--navbar-height) - var(--footer-height) - 20px);
}

html {
  min-height: 100dvh;
}

body {
  min-height: calc(100dvh - 50px);
}

@layer utilities {
  .error-message {
    @apply text-xs font-normal leading-6 text-red-500;
  }
}

ol,
ul {
  list-style: auto;
}

h1 {
  font-size: 24px;
  font-weight: 700;
  line-height: 32px;
}

h2 {
  font-size: 20px;
  font-weight: 700;
  line-height: 28px;
}

h3 {
  font-size: 18px;
  font-weight: 700;
  line-height: 28px;
}

h4 {
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
}

h5 {
  font-size: 14px;
  font-weight: 700;
  line-height: 20px;
}

h6 {
  font-size: 12px;
  font-weight: 700;
  line-height: 16px;
}

/* Ghost CMS */
.gh-content {
  @apply mx-auto max-w-[50%];
}
.gh-content > [id]:not(:first-child) {
  @apply !mt-14;
}
.gh-content {
  h1 {
    @apply !text-[38px] !leading-[-.02em];
  }
}
.gh-content > * + * {
  @apply !mb-0;
}
.gh-content > [id] + p {
  @apply !mt-3;
}
.gh-content :not(.kg-card):not(table):not([id]) + :is(.kg-card, table) {
  @apply mt-12;
}
.gh-content :is(.kg-card, table) + :not(.kg-card):not(table):not([id]) {
  @apply !mt-12;
}
.gh-content :is(ul, ol) {
  @apply !mt-7 pl-7;
}
.gh-content ul {
  @apply !list-disc;
}
.gh-content ol {
  @apply !list-decimal;
}
.gh-content :is(li + li, li :is(ul, ol)) {
  @apply mt-2;
}

/* Uppy Custom Styles */
.uppy-DashboardContent-addMore,
.uppy-DashboardContent-back {
  @apply !border !border-gray-300 !bg-transparent !px-5 !py-2.5 !text-gray-700 hover:!bg-gray-100 hover:!bg-inherit;
  border-radius: 8px !important;
  border: 1px solid #d0d5dd !important;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}

.uppy-Dashboard-browse {
  @apply !border-purple-500 !text-purple-500;
}

.uppy-DashboardContent-bar,
.uppy-Dashboard-progressindicators {
  @apply !bg-white;
}

.uppy-DashboardContent-title {
  text-align: center !important;
  font-size: 20px !important;
  font-style: normal !important;
  font-weight: 700 !important;
  line-height: 150% !important;
  top: unset !important;
}

button.uppy-StatusBar-actionBtn,
.uppy-c-btn-primary.uppy-Dashboard-FileCard-actionsBtn {
  @apply !bg-primary-700 hover:!bg-primary-600 !h-10 !items-center !justify-center !gap-2 !rounded-lg !px-5 !py-2.5 !text-white !ring-0;
}

.uppy-StatusBar.is-uploading {
  @apply h-16 !bg-white p-2.5;
}
.uppy-StatusBar-content[aria-label="Uploading"] {
  @apply flex items-center justify-center !rounded-lg !bg-purple-500 !px-5 !py-2.5 !text-sm !font-medium !text-white [&_svg]:!fill-white;
}
.uppy-StatusBar-actionCircleBtn[aria-label="Cancel"] {
  @apply [&_circle]:!fill-primary-500 bg-primary-500 flex size-10 items-center justify-center rounded-full !ring-0 [&_svg]:!size-6;
}

.uppy-Dashboard-AddFiles {
  @apply !border-none;
}
.uppy-Dashboard-innerWrap {
  @apply border !border-dashed;
}

.uppy-Informer {
  @apply !hidden;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Style for scrollbar */
.select-options::-webkit-scrollbar {
  width: 8px;
}

.select-options::-webkit-scrollbar-thumb {
  background-color: #bdbaba;
  border-radius: 20px;
}

.select-options::-webkit-scrollbar-thumb:hover {
  background-color: #999;
}

/* Message loader */
.message-loader {
  width: 30px;
  aspect-ratio: 2;
  --_g: no-repeat radial-gradient(circle closest-side, #a5a2a2 90%, #a5a2a200);
  background:
    var(--_g) 0% 50%,
    var(--_g) 50% 50%,
    var(--_g) 100% 50%;
  background-size: calc(100% / 3) 50%;
  animation: l3 1s infinite linear;
}
@keyframes l3 {
  20% {
    background-position:
      0% 0%,
      50% 50%,
      100% 50%;
  }
  40% {
    background-position:
      0% 100%,
      50% 0%,
      100% 50%;
  }
  60% {
    background-position:
      0% 50%,
      50% 100%,
      100% 0%;
  }
  80% {
    background-position:
      0% 50%,
      50% 50%,
      100% 100%;
  }
}

/* React PDF Viewer */
.rpv-default-layout__container {
  @apply overflow-hidden rounded-lg;
}

* {
  scrollbar-width: auto;
  scrollbar-color: #bdbaba transparent;
}

*::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background-color: #bdbaba;
  border-radius: 20px;
}

*::-webkit-scrollbar-thumb:hover {
  background-color: #999;
}

html {
  @apply text-[#111928];
}

.timescape-root {
  display: flex;
  align-items: center;
  gap: 2px;
  width: fit-content;
  border: 1px solid #bbb;
  padding: 5px;
  user-select: none;
  border-radius: 10px;
  transition: 100ms;
}

.timescape-root:focus-within {
  outline: 1px solid #1a56db;
  border-color: #1a56db;
}

.timescape-input {
  font-variant-numeric: tabular-nums;
  height: fit-content;
  font-size: 18px;
  color: #333;
  border: none;
  outline: none;
  cursor: default;
  user-select: none;
  max-width: 50px;
}

.timescape-input::selection {
  background: none;
}

.timescape-input:focus {
  background-color: #1a56db;
  color: #fff;
  border-radius: 6px;
  padding: 2px;
}

.separator {
  font-size: 80%;
  color: #8c8c8c;
  margin: 0;
}

input[type="number"].number-no-arrows::-webkit-inner-spin-button,
input[type="number"].number-no-arrows::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.uppy-editor-hide-back .uppy-DashboardContent-back {
  display: none !important;
}

.uppy-Dashboard-inner {
  width: 100% !important;
}

/* Dashed rotate border */
.perforated-bottom {
  position: relative;
}

.perforated-bottom::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 11px;
  background: repeating-linear-gradient(
    90deg,
    #9ca3af 0px,
    #9ca3af 3px,
    transparent 3px,
    transparent 8px
  );
}

.perforated-right {
  position: relative;
}

.perforated-right::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  right: -1px;
  width: 11px;
  background: repeating-linear-gradient(
    0deg,
    #9ca3af 0px,
    #9ca3af 3px,
    transparent 3px,
    transparent 8px
  );
}
