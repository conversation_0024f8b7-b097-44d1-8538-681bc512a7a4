"use client";

import { useParams, usePathname } from "next/navigation";
import { useEffect } from "react";

import { usePatient } from "@/components/modules/common/patients/detail/hooks/use-patient";
import { usePatientStore } from "@/stores/patient-store";

export default function PatientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { currentPatient, setCurrentPatient } = usePatientStore();
  const { patientId } = useParams();
  const pathname = usePathname();

  const { data: patient } = usePatient({ patientId: patientId as string });

  useEffect(() => {
    if (
      (!currentPatient || currentPatient.id !== patientId) &&
      patient &&
      currentPatient !== null
    ) {
      setCurrentPatient(patient);
    }

    if (currentPatient === null) {
      setCurrentPatient(undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [patient, patientId, setCurrentPatient, pathname]);

  return children;
}
