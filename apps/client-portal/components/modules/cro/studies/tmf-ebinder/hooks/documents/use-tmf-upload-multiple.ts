import { useMutation, useQueryClient } from "@tanstack/react-query";

import api from "@/lib/apis";
import { TIMELINE_QUERY_KEYS } from "@/lib/apis/shared/timeline-types";
import { TMFeBinderMultipleUploadPayload } from "@/lib/apis/tmf-essential-document-files/types";

import { USE_TMF_DOCUMENTS_QUERY_KEY } from "./use-tmf-documents";

export const useUploadTMFMultiple = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: TMFeBinderMultipleUploadPayload) =>
      api.tmfEssentialDocumentFiles.uploadMultiple(payload),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: [USE_TMF_DOCUMENTS_QUERY_KEY],
      });
      // Invalidate TMF timeline for all uploaded documents
      // Note: The upload response should contain the document IDs for proper invalidation
      // For now, we'll invalidate all TMF timeline queries to ensure consistency
      queryClient.invalidateQueries({
        queryKey: [TIMELINE_QUERY_KEYS.tmf],
      });
      queryClient.invalidateQueries({
        queryKey: [TIMELINE_QUERY_KEYS.infiniteTmf],
      });
    },
    retry: 0,
  });
};
