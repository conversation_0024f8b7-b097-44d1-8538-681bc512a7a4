import React from "react";

import { FormStatusBadge } from "@/components/ui/badge/edc/form-statuses";

type FilterStatus =
  | "notStarted"
  | "inProgress"
  | "activeQueries"
  | "readyForSdv"
  | "sourceDataVerified"
  | "formSigned"
  | "locked"
  | "archived";

interface FormStatusFiltersProps {
  filterStatus: string;
  onFilterClick: (status: FilterStatus) => void;
}

export const FormStatusFilters: React.FC<FormStatusFiltersProps> = ({
  filterStatus,
  onFilterClick,
}) => {
  return (
    <div className="flex items-center gap-2">
      <span className="mr-2 self-center text-sm font-medium text-gray-700">
        Filter columns by:
      </span>
      <FormStatusBadge
        variant="notStarted"
        onClick={() => onFilterClick("notStarted")}
        className="cursor-pointer before:hidden"
        isActive={filterStatus === "notStarted"}
      />
      <FormStatusBadge
        variant="inProgress"
        onClick={() => onFilterClick("inProgress")}
        className="cursor-pointer before:hidden"
        isActive={filterStatus === "inProgress"}
      />
      <FormStatusBadge
        variant="activeQueries"
        onClick={() => onFilterClick("activeQueries")}
        className="cursor-pointer before:hidden"
        isActive={filterStatus === "activeQueries"}
      />
      <FormStatusBadge
        variant="readyForSdv"
        onClick={() => onFilterClick("readyForSdv")}
        className="cursor-pointer before:hidden"
        isActive={filterStatus === "readyForSdv"}
      />
      <FormStatusBadge
        variant="sourceDataVerified"
        onClick={() => onFilterClick("sourceDataVerified")}
        className="cursor-pointer before:hidden"
        isActive={filterStatus === "sourceDataVerified"}
      />
      <FormStatusBadge
        variant="formSigned"
        onClick={() => onFilterClick("formSigned")}
        className="cursor-pointer before:hidden"
        isActive={filterStatus === "formSigned"}
      />
      <FormStatusBadge
        variant="locked"
        onClick={() => onFilterClick("locked")}
        className="cursor-pointer before:hidden"
        isActive={filterStatus === "locked"}
      />
      <FormStatusBadge
        variant="archived"
        onClick={() => onFilterClick("archived")}
        className="cursor-pointer before:hidden"
        isActive={filterStatus === "archived"}
      />
    </div>
  );
};
