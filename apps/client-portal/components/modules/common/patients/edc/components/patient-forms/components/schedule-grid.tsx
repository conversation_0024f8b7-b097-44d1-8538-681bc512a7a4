import { Diff } from "lucide-react";
import React from "react";

import { StatusComponent } from "@/components/ui/badge/edc/progress-indicator";
import { Tooltip } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

import {
  FormPeriod,
  Forms,
  forms,
  formVisitProgresses,
  formVisits,
} from "../../patient-dashboard/data";

interface PeriodConfig {
  title: string;
  visits: Array<{
    id: string;
    name: string;
    subName: string;
    visitDay: number;
    visitWindow: number;
    period: FormPeriod;
    forms: Forms[];
  }>;
}

interface ScheduleGridProps {
  periodConfig: PeriodConfig[];
  filteredForms: Forms[];
  selectedVisits: Set<string>;
  selectedForms: Set<string>;
}

interface HiddenColumnGroup {
  startIndex: number;
  endIndex: number;
  insertAfterIndex: number;
  count: number;
}

interface HiddenRowGroup {
  startIndex: number;
  endIndex: number;
  insertAfterIndex: number;
  count: number;
}

const tooltipMessages = {
  notRequired: "No Form Required",
  notStarted: "Not Started",
  inProgress: "In Progress",
  readyForSdv: "Ready for SDV",
  sourceDataVerified: "Source Data Verified",
  completed: "Form Completed",
  containOpenQuery: "Has Open Queries",
};

// Helper function to generate tooltip content
const getHiddenColumnsTooltip = (count: number): string => {
  return `${count} column${count > 1 ? "s" : ""} hidden`;
};

const getHiddenRowsTooltip = (count: number): string => {
  return `${count} row${count > 1 ? "s" : ""} hidden`;
};

// Reusable HiddenColumnDivider component
const HiddenColumnDivider: React.FC<{ keyPrefix: string; count: number }> = ({
  keyPrefix,
  count,
}) => (
  <div key={keyPrefix} style={{ gridColumn: "span 1" }} className="relative">
    <Tooltip
      content={
        <span className="whitespace-nowrap">
          {getHiddenColumnsTooltip(count)}
        </span>
      }
      variant="primary"
      theme={{
        target: "relative z-20 h-full w-full",
      }}
    >
      <span className="invisible">X</span>
    </Tooltip>
    <div className="absolute top-0 z-[5] h-full w-full">
      <div className="perforated-right h-full w-full" />
    </div>
  </div>
);

// Reusable HiddenRowDivider component
const HiddenRowDivider: React.FC<{
  keyPrefix: string;
  count: number;
  colSpan: number;
}> = ({ keyPrefix, count, colSpan }) => (
  <div
    key={keyPrefix}
    className="relative"
    style={{ gridColumn: `span ${colSpan}` }}
  >
    <Tooltip
      content={
        <span className="whitespace-nowrap">{getHiddenRowsTooltip(count)}</span>
      }
      variant="primary"
      theme={{
        target: "relative z-20 h-full w-full",
      }}
    >
      <span className="invisible">X</span>
    </Tooltip>
    <div className="absolute top-0 z-[5] h-full w-full">
      <div className="perforated-bottom h-full w-full" />
    </div>
  </div>
);

export const ScheduleGrid: React.FC<ScheduleGridProps> = ({
  periodConfig,
  filteredForms,
  selectedVisits,
  selectedForms,
}) => {
  // Calculate hidden column groups for visual indicators
  const getHiddenColumnGroups = (): HiddenColumnGroup[] => {
    const allVisits = formVisits;
    const hiddenGroups: HiddenColumnGroup[] = [];
    let currentGroup: { start: number; end: number } | null = null;

    for (let i = 0; i < allVisits.length; i++) {
      const visit = allVisits[i];
      const isHidden = !selectedVisits.has(visit.id);

      if (isHidden) {
        if (currentGroup === null) {
          currentGroup = { start: i, end: i };
        } else {
          currentGroup.end = i;
        }
      } else {
        if (currentGroup !== null) {
          // Find the insertion point in the visible grid
          const visibleVisitsBeforeGroup = allVisits
            .slice(0, currentGroup.start)
            .filter((v) => selectedVisits.has(v.id)).length;

          hiddenGroups.push({
            startIndex: currentGroup.start,
            endIndex: currentGroup.end,
            insertAfterIndex: visibleVisitsBeforeGroup,
            count: currentGroup.end - currentGroup.start + 1,
          });
          currentGroup = null;
        }
      }
    }

    // Handle case where hidden columns are at the end
    if (currentGroup !== null) {
      const visibleVisitsBeforeGroup = allVisits
        .slice(0, currentGroup.start)
        .filter((v) => selectedVisits.has(v.id)).length;

      hiddenGroups.push({
        startIndex: currentGroup.start,
        endIndex: currentGroup.end,
        insertAfterIndex: visibleVisitsBeforeGroup,
        count: currentGroup.end - currentGroup.start + 1,
      });
    }

    return hiddenGroups;
  };

  // Calculate hidden row groups for visual indicators
  const getHiddenRowGroups = (): HiddenRowGroup[] => {
    const allForms = forms;
    const hiddenGroups: HiddenRowGroup[] = [];
    let currentGroup: { start: number; end: number } | null = null;

    for (let i = 0; i < allForms.length; i++) {
      const form = allForms[i];
      const isHidden = !selectedForms.has(form.id);

      if (isHidden) {
        if (currentGroup === null) {
          currentGroup = { start: i, end: i };
        } else {
          currentGroup.end = i;
        }
      } else {
        if (currentGroup !== null) {
          // Find the insertion point in the visible grid
          const visibleFormsBeforeGroup = allForms
            .slice(0, currentGroup.start)
            .filter((f: Forms) => selectedForms.has(f.id)).length;

          hiddenGroups.push({
            startIndex: currentGroup.start,
            endIndex: currentGroup.end,
            insertAfterIndex: visibleFormsBeforeGroup,
            count: currentGroup.end - currentGroup.start + 1,
          });
          currentGroup = null;
        }
      }
    }

    // Handle case where hidden rows are at the end
    if (currentGroup !== null) {
      const visibleFormsBeforeGroup = allForms
        .slice(0, currentGroup.start)
        .filter((f: Forms) => selectedForms.has(f.id)).length;

      hiddenGroups.push({
        startIndex: currentGroup.start,
        endIndex: currentGroup.end,
        insertAfterIndex: visibleFormsBeforeGroup,
        count: currentGroup.end - currentGroup.start + 1,
      });
    }

    return hiddenGroups;
  };

  const hiddenColumnGroups = getHiddenColumnGroups();
  const hiddenRowGroups = getHiddenRowGroups();

  // Generate column template with dividers
  const generateGridColumns = () => {
    const columns: string[] = [];
    let currentVisibleIndex = 0;

    // Add the form name column
    columns.push("auto");

    // Add visible columns and dividers
    for (const config of periodConfig) {
      for (let i = 0; i < config.visits.length; i++) {
        // Check if we need to insert a divider before this column
        const dividerForThisPosition = hiddenColumnGroups.find(
          (group) => group.insertAfterIndex === currentVisibleIndex,
        );

        if (dividerForThisPosition) {
          columns.push("10px"); // Black divider column
        }

        columns.push("1fr"); // Visible visit column
        currentVisibleIndex++;
      }
    }

    // Check for dividers at the end
    const finalDivider = hiddenColumnGroups.find(
      (group) => group.insertAfterIndex === currentVisibleIndex,
    );
    if (finalDivider) {
      columns.push("10px");
    }

    return columns.join(" ");
  };

  // Generate header elements with dividers
  const generateHeaderElements = () => {
    const elements: React.ReactElement[] = [];
    let currentVisibleIndex = 0;

    for (const config of periodConfig) {
      for (let i = 0; i < config.visits.length; i++) {
        const visit = config.visits[i];

        // Check if we need to insert a divider before this visit
        const dividerForThisPosition = hiddenColumnGroups.find(
          (group) => group.insertAfterIndex === currentVisibleIndex,
        );

        if (dividerForThisPosition) {
          elements.push(
            <HiddenColumnDivider
              key={`divider-before-${visit.id}`}
              keyPrefix={`divider-before-${visit.id}`}
              count={dividerForThisPosition.count}
            />,
          );
        }

        elements.push(
          <div
            key={`header-${visit.id}`}
            className="flex items-center whitespace-nowrap border border-gray-100 px-2.5 py-2 text-center text-[10px] font-semibold leading-[18px]"
          >
            {visit.name} / Day {visit.visitDay} <Diff size={12} />{" "}
            {visit.visitWindow}d
          </div>,
        );

        currentVisibleIndex++;
      }
    }

    // Check for dividers at the end
    const finalDivider = hiddenColumnGroups.find(
      (group) => group.insertAfterIndex === currentVisibleIndex,
    );
    if (finalDivider) {
      elements.push(
        <HiddenColumnDivider
          key="divider-final"
          keyPrefix="divider-final"
          count={finalDivider.count}
        />,
      );
    }

    return elements;
  };

  // Generate form cell elements with dividers
  const generateFormCells = (form: Forms) => {
    const elements: React.ReactElement[] = [];
    let currentVisibleIndex = 0;

    for (const config of periodConfig) {
      for (const visit of config.visits) {
        // Check if we need to insert a divider before this visit
        const dividerForThisPosition = hiddenColumnGroups.find(
          (group) => group.insertAfterIndex === currentVisibleIndex,
        );

        if (dividerForThisPosition) {
          elements.push(
            <HiddenColumnDivider
              key={`divider-${form.id}-before-${visit.id}`}
              keyPrefix={`divider-${form.id}-before-${visit.id}`}
              count={dividerForThisPosition.count}
            />,
          );
        }

        const formVisit = config.visits.find(
          (visit2) =>
            visit.forms.some((formVisit) => formVisit.id === form.id) &&
            visit2.id === visit.id,
        );

        const formVisitProgress = formVisitProgresses.find(
          (formVisitProgress) =>
            formVisitProgress.formVisitId === visit.id &&
            formVisitProgress.id === form.id,
        );

        const notRequiredForm = !formVisit;

        elements.push(
          <div
            key={`${form.id}-${visit.id}`}
            className={cn(
              "relative whitespace-nowrap border border-[#EAECF0] px-4 py-3 text-center text-[10px] font-medium leading-[18px] text-gray-500",
              notRequiredForm && "bg-[#F9FAFB]",
            )}
          >
            <Tooltip
              content={
                tooltipMessages[formVisitProgress?.variant ?? "notRequired"]
              }
              variant="primary"
              theme={{
                target: "w-ful",
              }}
            >
              <span className="invisible">X</span>
              <div
                className={cn(
                  "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2",
                  notRequiredForm && "hidden",
                )}
              >
                <StatusComponent
                  percentage={formVisitProgress?.progress ?? 0}
                  size={16}
                  strokeWidth={2}
                  variant={formVisitProgress?.variant ?? "notStarted"}
                />
              </div>
            </Tooltip>
          </div>,
        );

        currentVisibleIndex++;
      }
    }

    // Check for dividers at the end
    const finalDivider = hiddenColumnGroups.find(
      (group) => group.insertAfterIndex === currentVisibleIndex,
    );
    if (finalDivider) {
      elements.push(
        <HiddenColumnDivider
          key={`divider-${form.id}-final`}
          keyPrefix={`divider-${form.id}-final`}
          count={finalDivider.count}
        />,
      );
    }

    return elements;
  };

  // Generate grid template rows with exact heights for dividers
  const generateGridRows = () => {
    const rows: string[] = [];
    let currentVisibleIndex = 0;

    // Add header rows
    rows.push("auto"); // Form Column Header
    rows.push("auto"); // Visit Windows / Days row header

    for (const form of filteredForms) {
      // Check if we need to insert a row divider before this form
      const dividerForThisPosition = hiddenRowGroups.find(
        (group) => group.insertAfterIndex === currentVisibleIndex,
      );

      if (dividerForThisPosition) {
        rows.push("10px"); // Exact height for divider row
      }

      rows.push("minmax(auto, 1fr)"); // Flexible height for form row
      currentVisibleIndex++;
    }

    // Check for row dividers at the end
    const finalRowDivider = hiddenRowGroups.find(
      (group) => group.insertAfterIndex === currentVisibleIndex,
    );
    if (finalRowDivider) {
      rows.push("10px"); // Exact height for final divider row
    }

    return rows.join(" ");
  };

  // Generate form rows with hidden row dividers
  const generateFormRowsWithDividers = () => {
    const elements: React.ReactElement[] = [];
    let currentVisibleIndex = 0;
    const totalVisibleColumns =
      periodConfig.reduce((sum, config) => sum + config.visits.length, 0) +
      hiddenColumnGroups.length;

    for (const form of filteredForms) {
      // Check if we need to insert a row divider before this form
      const dividerForThisPosition = hiddenRowGroups.find(
        (group) => group.insertAfterIndex === currentVisibleIndex,
      );

      if (dividerForThisPosition) {
        elements.push(
          <HiddenRowDivider
            key={`row-divider-before-${form.id}`}
            keyPrefix={`row-divider-before-${form.id}`}
            count={dividerForThisPosition.count}
            colSpan={totalVisibleColumns + 1} // +1 for the form name column
          />,
        );
      }

      // Add the actual form row
      elements.push(
        <React.Fragment key={`form-${form.id}`}>
          {/* Form name column */}
          <div
            key={`form-name-${form.id}`}
            className="whitespace-nowrap border border-gray-100 px-4 py-3 text-center text-[10px] font-medium leading-[18px] text-gray-500"
          >
            {form.name}
          </div>

          {/* Form cells with dividers */}
          {generateFormCells(form)}
        </React.Fragment>,
      );

      currentVisibleIndex++;
    }

    // Check for row dividers at the end
    const finalRowDivider = hiddenRowGroups.find(
      (group) => group.insertAfterIndex === currentVisibleIndex,
    );
    if (finalRowDivider) {
      elements.push(
        <HiddenRowDivider
          key="row-divider-final"
          keyPrefix="row-divider-final"
          count={finalRowDivider.count}
          colSpan={totalVisibleColumns + 1} // +1 for the form name column
        />,
      );
    }

    return elements;
  };

  return (
    <div
      className="grid flex-1 overflow-x-auto rounded-lg border"
      style={{
        gridTemplateColumns: generateGridColumns(),
        gridTemplateRows: generateGridRows(),
      }}
    >
      {/* Form Column Header */}
      <div className="whitespace-nowrap bg-gray-200 px-2.5 py-2 text-center text-xs font-bold leading-[18px]">
        Visit / Activity
      </div>

      {/* Period Column Headers with dividers */}
      {(() => {
        const headerElements: React.ReactElement[] = [];
        let currentVisibleIndex = 0;

        for (const config of periodConfig) {
          // Check if we need a divider before this period
          const dividerBeforePeriod = hiddenColumnGroups.find(
            (group) => group.insertAfterIndex === currentVisibleIndex,
          );

          if (dividerBeforePeriod) {
            headerElements.push(
              <HiddenColumnDivider
                key={`period-divider-before-${config.title}`}
                keyPrefix={`period-divider-before-${config.title}`}
                count={dividerBeforePeriod.count}
              />,
            );
          }

          // Calculate how many columns this period should span (including any dividers within)
          let periodSpan = config.visits.length;
          const periodStartIndex = currentVisibleIndex;
          const periodEndIndex = currentVisibleIndex + config.visits.length;

          // Count dividers that fall within this period
          const dividersInPeriod = hiddenColumnGroups.filter(
            (group) =>
              group.insertAfterIndex > periodStartIndex &&
              group.insertAfterIndex < periodEndIndex,
          ).length;

          periodSpan += dividersInPeriod;

          headerElements.push(
            <div
              key={config.title}
              className="whitespace-nowrap bg-gray-200 px-2.5 py-2 text-center text-xs font-bold leading-[18px]"
              style={{ gridColumn: `span ${periodSpan}` }}
            >
              {config.title}
            </div>,
          );

          currentVisibleIndex += config.visits.length;
        }

        // Check for final divider
        const finalPeriodDivider = hiddenColumnGroups.find(
          (group) => group.insertAfterIndex === currentVisibleIndex,
        );
        if (finalPeriodDivider) {
          headerElements.push(
            <HiddenColumnDivider
              key="period-divider-final"
              keyPrefix="period-divider-final"
              count={finalPeriodDivider.count}
            />,
          );
        }

        return headerElements;
      })()}

      {/* Visit Windows / Days row header */}
      <div className="whitespace-nowrap border border-gray-100 px-2.5 py-2 text-center text-[10px] font-semibold leading-[18px]">
        Visit Windows / Days
      </div>

      {/* Visit headers with dividers */}
      {generateHeaderElements()}

      {/* Form rows with hidden row dividers */}
      {generateFormRowsWithDividers()}
    </div>
  );
};
