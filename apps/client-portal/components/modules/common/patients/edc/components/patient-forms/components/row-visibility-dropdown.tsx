import { ChevronDown } from "lucide-react";
import React from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownLabel,
  DropdownTrigger,
} from "@/components/ui/dropdown/dropdown";
import { CheckboxField } from "@/components/ui/form";

import { forms } from "../../patient-dashboard/data";

interface RowVisibilityDropdownProps {
  selectedForms: Set<string>;
  onFormSelection: (formId: string, checked: boolean) => void;
  onShowAllForms: () => void;
}

export const RowVisibilityDropdown: React.FC<RowVisibilityDropdownProps> = ({
  selectedForms,
  onFormSelection,
  onShowAllForms,
}) => {
  return (
    <Dropdown>
      <DropdownTrigger>
        <Button variant="outline" className="flex items-center gap-2 text-sm">
          Rows ({selectedForms.size} of {forms.length})
          <ChevronDown size={16} />
        </Button>
      </DropdownTrigger>
      <DropdownContent className="max-h-96 w-64 overflow-y-auto p-0">
        <div className="flex items-center justify-between border-b border-gray-200 p-3">
          <DropdownLabel className="p-0 text-sm font-medium">
            Row Visibility
          </DropdownLabel>
          <button
            onClick={onShowAllForms}
            className="text-sm font-medium text-purple-600 hover:text-purple-800"
          >
            Show All
          </button>
        </div>
        <div className="space-y-2 p-2">
          {forms.map((form) => (
            <div
              key={form.id}
              className="flex items-center space-x-2 rounded p-2 hover:bg-gray-50"
            >
              <CheckboxField
                id={`form-${form.id}`}
                name={`form-${form.id}`}
                checked={selectedForms.has(form.id)}
                onChange={(e) => onFormSelection(form.id, e.target.checked)}
                className="text-purple-600"
              />
              <label
                htmlFor={`form-${form.id}`}
                className="flex-1 cursor-pointer text-sm text-gray-700"
              >
                {form.name}
              </label>
            </div>
          ))}
        </div>
      </DropdownContent>
    </Dropdown>
  );
};
