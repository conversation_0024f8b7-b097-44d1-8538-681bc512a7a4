import { ChevronDown } from "lucide-react";
import React from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownLabel,
  DropdownTrigger,
} from "@/components/ui/dropdown/dropdown";
import { CheckboxField } from "@/components/ui/form";

import { formVisits } from "../../patient-dashboard/data";

interface ColumnVisibilityDropdownProps {
  selectedVisits: Set<string>;
  onVisitSelection: (visitId: string, checked: boolean) => void;
  onShowAllVisits: () => void;
}

export const ColumnVisibilityDropdown: React.FC<
  ColumnVisibilityDropdownProps
> = ({ selectedVisits, onVisitSelection, onShowAllVisits }) => {
  return (
    <Dropdown>
      <DropdownTrigger>
        <Button variant="outline" className="flex items-center gap-2 text-sm">
          Columns ({selectedVisits.size} of {formVisits.length})
          <ChevronDown size={16} />
        </Button>
      </DropdownTrigger>
      <DropdownContent className="max-h-96 w-64 overflow-y-auto p-0">
        <div className="flex items-center justify-between border-b border-gray-200 p-3">
          <DropdownLabel className="p-0 text-sm font-medium">
            Column Visibility
          </DropdownLabel>
          <button
            onClick={onShowAllVisits}
            className="text-sm font-medium text-purple-600 hover:text-purple-800"
          >
            Show All
          </button>
        </div>
        <div className="space-y-2 p-2">
          {formVisits.map((visit) => (
            <div
              key={visit.id}
              className="flex items-center space-x-2 rounded p-2 hover:bg-gray-50"
            >
              <CheckboxField
                id={`visit-${visit.id}`}
                name={`visit-${visit.id}`}
                checked={selectedVisits.has(visit.id)}
                onChange={(e) => onVisitSelection(visit.id, e.target.checked)}
                className="text-purple-600"
              />
              <label
                htmlFor={`visit-${visit.id}`}
                className="flex-1 cursor-pointer text-sm text-gray-700"
              >
                {visit.name} / Day {visit.visitDay} ± {visit.visitWindow}d
              </label>
            </div>
          ))}
        </div>
      </DropdownContent>
    </Dropdown>
  );
};
